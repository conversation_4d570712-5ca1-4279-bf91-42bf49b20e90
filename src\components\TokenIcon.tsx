import { useState, useEffect } from "react";
import { cn } from "@/lib/utils";

interface TokenIconProps {
  iconPath: string;
  symbol: string;
  className?: string;
  size?: "sm" | "md" | "lg";
}

export function TokenIcon({
  iconPath,
  symbol,
  className,
  size = "md",
}: TokenIconProps) {
  const [imageSrc, setImageSrc] = useState<string>("");
  const [hasError, setHasError] = useState(false);

  const sizeClasses = {
    sm: "w-4 h-4",
    md: "w-6 h-6",
    lg: "w-8 h-8",
  };

  useEffect(() => {
    const loadIcon = async () => {
      try {
        setHasError(false);

        // iconPath is now just the filename (e.g., "WBTC.svg")
        const filename = iconPath.includes("/")
          ? iconPath.split("/").pop() || ""
          : iconPath;

        console.log(`TokenIcon Debug for ${symbol}:`, {
          iconPath,
          filename,
        });

        try {
          // Try to dynamically import the specific icon
          const iconModule = await import(`../assets/tokens/${filename}?url`);
          console.log(`Found icon for ${symbol}`);
          setImageSrc(iconModule.default);
        } catch (iconError) {
          console.log(`Icon not found for ${symbol}, trying fallback`);
          // Try fallback to default Token.svg
          try {
            const defaultModule = await import('../assets/tokens/Token.svg?url');
            console.log(`Using fallback icon for ${symbol}`);
            setImageSrc(defaultModule.default);
          } catch (fallbackError) {
            console.log(`No fallback icon found for ${symbol}`);
            throw new Error(`No icon found for ${symbol}`);
          }
        }
      } catch (error) {
        console.warn(`Failed to load icon for ${symbol}:`, error);
        setHasError(true);
      }
    };

    loadIcon();
  }, [iconPath, symbol]);

  if (hasError && !imageSrc) {
    // Fallback to a simple colored circle with the first letter of the symbol
    return (
      <div
        className={cn(
          "rounded-full bg-gray-600 flex items-center justify-center text-white font-medium text-xs",
          sizeClasses[size],
          className
        )}
      >
        {symbol.charAt(0)}
      </div>
    );
  }

  return (
    <img
      src={imageSrc}
      alt={symbol}
      className={cn("object-contain", sizeClasses[size], className)}
      onError={() => {
        setHasError(true);
        setImageSrc("");
      }}
    />
  );
}
