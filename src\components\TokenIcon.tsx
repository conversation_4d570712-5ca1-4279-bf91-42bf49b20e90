import { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';

// Import all token icons using Vite's glob import
const tokenIcons = import.meta.glob('../assets/tokens/*.svg', { as: 'url', eager: true });

interface TokenIconProps {
  iconPath: string;
  symbol: string;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

export function TokenIcon({ iconPath, symbol, className, size = 'md' }: TokenIconProps) {
  const [imageSrc, setImageSrc] = useState<string>('');
  const [hasError, setHasError] = useState(false);

  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8'
  };

  useEffect(() => {
    try {
      setHasError(false);

      // iconPath is now just the filename (e.g., "WBTC.svg")
      const filename = iconPath.includes('/') ? iconPath.split('/').pop() || '' : iconPath;
      const modulePath = `../assets/tokens/${filename}`;



      // Check if the icon exists in our imported icons
      if (tokenIcons[modulePath]) {
        setImageSrc(tokenIcons[modulePath]);
      } else {
        // Try fallback to default Token.svg
        const defaultPath = '../assets/tokens/Token.svg';
        if (tokenIcons[defaultPath]) {
          setImageSrc(tokenIcons[defaultPath]);
        } else {
          throw new Error(`No icon found for ${symbol}`);
        }
      }
    } catch (error) {
      console.warn(`Failed to load icon for ${symbol}:`, error);
      setHasError(true);
    }
  }, [iconPath, symbol]);

  if (hasError && !imageSrc) {
    // Fallback to a simple colored circle with the first letter of the symbol
    return (
      <div className={cn(
        'rounded-full bg-gray-600 flex items-center justify-center text-white font-medium text-xs',
        sizeClasses[size],
        className
      )}>
        {symbol.charAt(0)}
      </div>
    );
  }

  return (
    <img
      src={imageSrc}
      alt={symbol}
      className={cn('object-contain', sizeClasses[size], className)}
      onError={() => {
        setHasError(true);
        setImageSrc('');
      }}
    />
  );
}
