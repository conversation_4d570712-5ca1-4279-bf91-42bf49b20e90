import { useState, useEffect } from "react";
import { cn } from "@/lib/utils";

// Import all the token icons statically
import TokenDefault from "../assets/tokens/Token.svg?url";
import ATOM from "../assets/tokens/ATOM.svg?url";
import BLUR from "../assets/tokens/BLUR.svg?url";
import BUSD from "../assets/tokens/BUSD.svg?url";
import ETH from "../assets/tokens/ETH.svg?url";
import EVMOS from "../assets/tokens/EVMOS.svg?url";
import GMX from "../assets/tokens/GMX.svg?url";
import IBCX from "../assets/tokens/IBCX.svg?url";
import IRIS from "../assets/tokens/IRIS.svg?url";
import KUJI from "../assets/tokens/KUJI.svg?url";
import LSI from "../assets/tokens/LSI.svg?url";
import LUNA from "../assets/tokens/LUNA.svg?url";
import OKB from "../assets/tokens/OKB.svg?url";
import OKT from "../assets/tokens/OKT.svg?url";
import OSMO from "../assets/tokens/OSMO.svg?url";
import STRD from "../assets/tokens/STRD.svg?url";
import SWTH from "../assets/tokens/SWTH.svg?url";
import USC from "../assets/tokens/USC.svg?url";
import USD from "../assets/tokens/USD.svg?url";
import USDC from "../assets/tokens/USDC.svg?url";
import WBTC from "../assets/tokens/WBTC.svg?url";
import YieldUSD from "../assets/tokens/YieldUSD.svg?url";
import ZIL from "../assets/tokens/ZIL.svg?url";
import ampLUNA from "../assets/tokens/ampLUNA.svg?url";
import axlUSDC from "../assets/tokens/axlUSDC.svg?url";
import bNEO from "../assets/tokens/bNEO.svg?url";
import rATOM from "../assets/tokens/rATOM.svg?url";
import rSWTH from "../assets/tokens/rSWTH.svg?url";
import stATOM from "../assets/tokens/stATOM.svg?url";
import stEVMOS from "../assets/tokens/stEVMOS.svg?url";
import stLUNA from "../assets/tokens/stLUNA.svg?url";
import stOSMO from "../assets/tokens/stOSMO.svg?url";
import wstETH from "../assets/tokens/wstETH.svg?url";

// Create a mapping of token names to their imported icons
const tokenIconMap: Record<string, string> = {
  "Token.svg": TokenDefault,
  "ATOM.svg": ATOM,
  "BLUR.svg": BLUR,
  "BUSD.svg": BUSD,
  "ETH.svg": ETH,
  "EVMOS.svg": EVMOS,
  "GMX.svg": GMX,
  "IBCX.svg": IBCX,
  "IRIS.svg": IRIS,
  "KUJI.svg": KUJI,
  "LSI.svg": LSI,
  "LUNA.svg": LUNA,
  "OKB.svg": OKB,
  "OKT.svg": OKT,
  "OSMO.svg": OSMO,
  "STRD.svg": STRD,
  "SWTH.svg": SWTH,
  "USC.svg": USC,
  "USD.svg": USD,
  "USDC.svg": USDC,
  "WBTC.svg": WBTC,
  "YieldUSD.svg": YieldUSD,
  "ZIL.svg": ZIL,
  "ampLUNA.svg": ampLUNA,
  "axlUSDC.svg": axlUSDC,
  "bNEO.svg": bNEO,
  "rATOM.svg": rATOM,
  "rSWTH.svg": rSWTH,
  "stATOM.svg": stATOM,
  "stEVMOS.svg": stEVMOS,
  "stLUNA.svg": stLUNA,
  "stOSMO.svg": stOSMO,
  "wstETH.svg": wstETH,
};

interface TokenIconProps {
  iconPath: string;
  symbol: string;
  className?: string;
  size?: "sm" | "md" | "lg";
}

export function TokenIcon({
  iconPath,
  symbol,
  className,
  size = "md",
}: TokenIconProps) {
  const [imageSrc, setImageSrc] = useState<string>("");
  const [hasError, setHasError] = useState(false);

  const sizeClasses = {
    sm: "w-4 h-4",
    md: "w-6 h-6",
    lg: "w-8 h-8",
  };

  useEffect(() => {
    try {
      setHasError(false);

      // iconPath is now just the filename (e.g., "WBTC.svg")
      const filename = iconPath.includes("/")
        ? iconPath.split("/").pop() || ""
        : iconPath;

      // Look up the icon in our static mapping
      if (tokenIconMap[filename]) {
        setImageSrc(tokenIconMap[filename]);
      } else {
        // Use fallback Token.svg
        setImageSrc(tokenIconMap["Token.svg"]);
      }
    } catch (error) {
      console.warn(`Failed to load icon for ${symbol}:`, error);
      setHasError(true);
    }
  }, [iconPath, symbol]);

  if (hasError && !imageSrc) {
    // Fallback to a simple colored circle with the first letter of the symbol
    return (
      <div
        className={cn(
          "rounded-full bg-gray-600 flex items-center justify-center text-white font-medium text-xs",
          sizeClasses[size],
          className
        )}
      >
        {symbol.charAt(0)}
      </div>
    );
  }

  return (
    <img
      src={imageSrc}
      alt={symbol}
      className={cn("object-contain", sizeClasses[size], className)}
      onError={() => {
        setHasError(true);
        setImageSrc("");
      }}
    />
  );
}
