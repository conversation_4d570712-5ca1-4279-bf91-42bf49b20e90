import { useState, useEffect, useCallback } from "react";
import { ArrowUpDown, Wallet, Loader2, CheckCircle } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { SwapCard } from "@/components/SwapCard";
import { BalanceSidebar } from "@/components/BalanceSidebar";
import { tokenService, type Token } from "@/services";
import { useToast } from "@/hooks/use-toast";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  swapFormSchema,
  type SwapFormData,
  validateBalance,
} from "@/schemas/swapSchema";
import { cn } from "@/utils/cn";

export function SwapInterface() {
  const [tokens, setTokens] = useState<Token[]>([]);
  const [sellToken, setSellToken] = useState<Token | null>(null);
  const [buyToken, setBuyToken] = useState<Token | null>(null);
  const [isUpdatingSell, setIsUpdatingSell] = useState(false);
  const [isBalanceSidebarOpen, setIsBalanceSidebarOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isSwapping, setIsSwapping] = useState(false);
  const [swapSuccess, setSwapSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  const form = useForm<SwapFormData>({
    resolver: zodResolver(swapFormSchema),
    defaultValues: {
      sellAmount: "",
      buyAmount: "",
      sellTokenSymbol: "",
      buyTokenSymbol: "",
    },
    mode: "onChange",
  });

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors, isValid },
    reset,
    trigger,
  } = form;

  const sellAmount = watch("sellAmount");
  const buyAmount = watch("buyAmount");

  useEffect(() => {
    const loadTokens = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const transformedTokens = await tokenService.getTokens();
        setTokens(transformedTokens);

        // Don't auto-select tokens - leave them as null to show "Select a token" placeholder
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : "Failed to load token data";
        setError(errorMessage);
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadTokens();
  }, [toast, setValue]);

  // Calculate exchange rate
  const getExchangeRate = useCallback(() => {
    if (!sellToken || !buyToken) return 0;
    return sellToken.priceUsd / buyToken.priceUsd;
  }, [sellToken, buyToken]);

  const handleAmountChange = (amount: string, type: "sell" | "buy") => {
    const isSell = type === "sell";
    const currentField = isSell ? "sellAmount" : "buyAmount";
    const otherField = isSell ? "buyAmount" : "sellAmount";

    setValue(currentField, amount);

    if (amount && !isNaN(parseFloat(amount))) {
      if (isSell) {
        setIsUpdatingSell(true);
        const rate = getExchangeRate();
        const calculatedAmount = (parseFloat(amount) * rate).toString();
        setValue(otherField, calculatedAmount);
        setIsUpdatingSell(false);
      } else if (!isUpdatingSell) {
        const rate = getExchangeRate();
        const calculatedAmount = (parseFloat(amount) / rate).toString();
        setValue(otherField, calculatedAmount);
      }
    } else {
      setValue(otherField, "");
    }
    trigger(["sellAmount", "buyAmount"]);
  };

  // Update amounts when tokens change
  useEffect(() => {
    if (sellAmount && !isNaN(parseFloat(sellAmount)) && sellToken && buyToken) {
      const rate = getExchangeRate();
      const calculatedBuyAmount = (parseFloat(sellAmount) * rate).toString();
      setValue("buyAmount", calculatedBuyAmount);
    }
  }, [sellToken, buyToken, sellAmount, getExchangeRate, setValue]);

  const handleSwapTokens = () => {
    if (!sellToken || !buyToken || isSwapping) return;

    const tempToken = sellToken;
    const tempAmount = sellAmount;
    setSellToken(buyToken);
    setBuyToken(tempToken);
    setValue("sellAmount", buyAmount);
    setValue("buyAmount", tempAmount);
    setValue("sellTokenSymbol", buyToken.symbol);
    setValue("buyTokenSymbol", tempToken.symbol);
  };

  const handleMaxClick = () => {
    if (!sellToken) return;
    handleAmountChange(sellToken.balance, "sell");
  };

  const handleTokenSelect = (token: Token, type: "sell" | "buy") => {
    const isSell = type === "sell";
    const currentToken = isSell ? sellToken : buyToken;
    const otherToken = isSell ? buyToken : sellToken;
    const setCurrentToken = isSell ? setSellToken : setBuyToken;
    const setOtherToken = isSell ? setBuyToken : setSellToken;
    const currentSymbolField = isSell ? "sellTokenSymbol" : "buyTokenSymbol";
    const otherSymbolField = isSell ? "buyTokenSymbol" : "sellTokenSymbol";
    const currentAmountField = isSell ? "sellAmount" : "buyAmount";
    const otherAmountField = isSell ? "buyAmount" : "sellAmount";
    const currentAmount = isSell ? sellAmount : buyAmount;
    const otherAmount = isSell ? buyAmount : sellAmount;

    if (!otherToken) {
      setCurrentToken(token);
      setValue(currentSymbolField, token.symbol);
      return;
    }

    if (token.symbol === otherToken.symbol) {
      // If selecting the same token as the other side, swap them
      setCurrentToken(token);
      setOtherToken(currentToken);
      const tempAmount = currentAmount;
      setValue(currentAmountField, otherAmount);
      setValue(otherAmountField, tempAmount);
      setValue(currentSymbolField, token.symbol);
      setValue(otherSymbolField, currentToken?.symbol || "");
    } else {
      setCurrentToken(token);
      setValue(currentSymbolField, token.symbol);
    }
    trigger(["sellTokenSymbol", "buyTokenSymbol"]);
  };

  const onSwapSubmit = async (data: SwapFormData) => {
    if (!sellToken || !buyToken) {
      toast({
        title: "Error",
        description: "Please select both tokens",
        variant: "destructive",
      });
      return;
    }

    if (!validateBalance(data.sellAmount, sellToken.balance)) {
      toast({
        title: "Insufficient Balance",
        description: `You don't have enough ${sellToken.symbol}. Available: ${sellToken.balance}`,
        variant: "destructive",
      });
      return;
    }

    try {
      setIsSwapping(true);
      setSwapSuccess(false);

      // Simulate API call delay
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // Simulate random success/failure (90% success rate)
      const isSuccess = Math.random() > 0.1;

      if (isSuccess) {
        setSwapSuccess(true);

        const sellAmount = parseFloat(data.sellAmount);
        const buyAmount = parseFloat(data.buyAmount);

        // Update balances
        const updateBalance = (
          token: Token,
          amount: number,
          isAdd: boolean
        ) => {
          const newBalance = isAdd
            ? parseFloat(token.balance) + amount
            : Math.max(0, parseFloat(token.balance) - amount);
          const newValue = newBalance * token.priceUsd;
          return {
            ...token,
            balance: newBalance.toFixed(4),
            value: `$${newValue.toLocaleString("en-US", {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            })}`,
          };
        };

        setTokens((prevTokens) =>
          prevTokens.map((token) =>
            token.symbol === sellToken.symbol
              ? updateBalance(token, sellAmount, false)
              : token.symbol === buyToken.symbol
              ? updateBalance(token, buyAmount, true)
              : token
          )
        );

        setSellToken((prev) =>
          prev ? updateBalance(prev, sellAmount, false) : null
        );
        setBuyToken((prev) =>
          prev ? updateBalance(prev, buyAmount, true) : null
        );

        toast({
          title: "Swap Successful!",
          description: `Successfully swapped ${data.sellAmount} ${sellToken.symbol} for ${data.buyAmount} ${buyToken.symbol}`,
          variant: "success" as const,
        });

        reset();
        setSwapSuccess(false);
        if (sellToken && buyToken) {
          setValue("sellTokenSymbol", sellToken.symbol);
          setValue("buyTokenSymbol", buyToken.symbol);
        }
      } else {
        throw new Error("Swap failed due to network issues");
      }
    } catch (error) {
      toast({
        title: "Swap Failed",
        description:
          error instanceof Error
            ? error.message
            : "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setIsSwapping(false);
    }
  };

  const formatExchangeRate = () => {
    if (!sellToken || !buyToken) return "Select tokens to see exchange rate";

    const rate = getExchangeRate();
    if (rate === 0) return "Exchange rate unavailable";

    if (rate > 1) {
      return `1 ${sellToken.symbol} = ${rate.toLocaleString()} ${
        buyToken.symbol
      }`;
    } else {
      return `1 ${sellToken.symbol} = ${rate.toFixed(8)} ${buyToken.symbol}`;
    }
  };

  const swapCardConfigs = [
    {
      title: "You sell",
      token: sellToken,
      amount: sellAmount,
      onAmountChange: (amount: string) => handleAmountChange(amount, "sell"),
      onTokenSelect: (token: Token) => handleTokenSelect(token, "sell"),
      showMax: true,
      onMaxClick: handleMaxClick,
      register: register("sellAmount"),
      error: errors.sellAmount?.message,
    },
    {
      title: "You buy",
      token: buyToken,
      amount: buyAmount,
      onAmountChange: (amount: string) => handleAmountChange(amount, "buy"),
      onTokenSelect: (token: Token) => handleTokenSelect(token, "buy"),
      showMax: false,
      onMaxClick: undefined,
      register: register("buyAmount"),
      error: errors.buyAmount?.message,
      className: "border-2 border-teal-500/30 bg-gray-800/60",
    },
  ];

  const getButtonContent = () => {
    if (isSwapping)
      return (
        <div className="flex items-center gap-2">
          <Loader2 className="h-4 w-4 animate-spin" />
          Swapping...
        </div>
      );
    if (swapSuccess)
      return (
        <div className="flex items-center gap-2">
          <CheckCircle className="h-4 w-4" />
          Swap Successful!
        </div>
      );
    return "Swap";
  };

  // Show loading or error state
  if (isLoading || error) {
    return (
      <div className="w-full max-w-md mx-auto">
        <div
          className={`${
            isLoading ? "flex items-center justify-center" : "text-center"
          } p-8`}
        >
          {isLoading ? (
            <>
              <Loader2 className="h-8 w-8 animate-spin text-white" />
              <span className="ml-2 text-white">Loading tokens...</span>
            </>
          ) : (
            <>
              <p className="text-red-400 mb-4">
                {error || "Failed to load tokens"}
              </p>
              <Button
                onClick={() => window.location.reload()}
                className="bg-white hover:bg-gray-100 text-black"
              >
                Retry
              </Button>
            </>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="w-full max-w-md mx-auto">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold text-white">Swap</h1>
        <div className="flex items-center gap-3">
          <Button
            variant="ghost"
            size="icon"
            disabled={isSwapping}
            className={cn(
              "rounded-full",
              isSwapping
                ? "text-gray-400/70 cursor-not-allowed"
                : "text-gray-400 hover:text-white hover:bg-gray-800"
            )}
            onClick={() => setIsBalanceSidebarOpen(true)}
          >
            <Wallet className="h-5 w-5" />
          </Button>
        </div>
      </div>

      {/* Swap Cards Container */}
      <div className="relative">
        {swapCardConfigs.map((config, index) => (
          <div key={config.title}>
            <SwapCard
              title={config.title}
              token={config.token}
              amount={config.amount}
              onAmountChange={config.onAmountChange}
              onTokenSelect={config.onTokenSelect}
              tokens={tokens}
              showMax={config.showMax}
              onMaxClick={config.onMaxClick}
              register={config.register}
              error={config.error}
              className={config.className}
              disabled={isSwapping}
            />

            {/* Swap Button between cards */}
            {index === 0 && (
              <div className="flex justify-center -my-3 relative z-10">
                <Button
                  onClick={handleSwapTokens}
                  variant="ghost"
                  size="icon"
                  className="text-white rounded-full border-4 border-gray-900 transition-all duration-200 bg-gray-800 hover:bg-gray-700 hover:scale-110"
                >
                  <ArrowUpDown className="h-4 w-4" />
                </Button>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Exchange Rate */}
      <div className="mt-4 p-3 bg-gray-800/40 rounded-lg border border-gray-700">
        <span className="w-full justify-between text-sm text-gray-300 p-2">
          {formatExchangeRate()}
        </span>
      </div>

      {/* Action Button */}
      <Button
        onClick={handleSubmit(onSwapSubmit)}
        disabled={
          isSwapping || !isValid || !sellToken || !buyToken || swapSuccess
        }
        className={cn(
          "w-full mt-6 font-medium py-3 rounded-xl transition-all duration-200",
          swapSuccess
            ? "bg-green-500 text-white cursor-not-allowed"
            : isSwapping || !isValid || !sellToken || !buyToken
            ? "bg-white/70 text-black/70 cursor-not-allowed"
            : "bg-white hover:bg-gray-100 text-black hover:scale-[1.02]"
        )}
      >
        {getButtonContent()}
      </Button>

      {/* Display form errors */}
      {(errors.sellTokenSymbol || errors.buyTokenSymbol) && (
        <div className="mt-2 text-sm text-red-400 text-center">
          {errors.sellTokenSymbol?.message || errors.buyTokenSymbol?.message}
        </div>
      )}

      {/* Balance Sidebar */}
      <BalanceSidebar
        open={isBalanceSidebarOpen}
        onOpenChange={setIsBalanceSidebarOpen}
        tokens={tokens}
      />
    </div>
  );
}
